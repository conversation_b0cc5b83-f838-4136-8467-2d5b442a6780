# frozen_string_literal: true

module Floww
  class AddLead
    include Sidekiq::Worker

    def perform(user_id, lead_type, new_tag = '', auto_assign = true)
      @user = User.find user_id
      return if @user.blank?

      floww_contact_id = @user.get_floww_contact_id
      @user.create_floww_contact if floww_contact_id.blank?

      lead_score = @user.calculate_lead_score
      premium_pitch = @user.premium_pitch

      FlowwApi.update_add_autopay_sales_lead_activity(@user.id,
                                                      lead_type:,
                                                      lead_score:,
                                                      release_build_number: @user.release_build_number,
                                                      package_to_pitch: @user.should_pitch_yearly_package? ? 'Yearly' : nil,
                                                      lead_source: premium_pitch.source,
                                                      new_tag:,
                                                      auto_assign:)

      SyncMixpanelUser.perform_in(1.minute, @user.id)
      Floww::UpdateAffiliatedParty.perform_in(5.minutes, @user.id)
    end
  end
end

